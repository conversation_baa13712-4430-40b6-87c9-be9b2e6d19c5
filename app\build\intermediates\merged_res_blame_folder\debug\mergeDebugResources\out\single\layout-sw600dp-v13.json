[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\fragment_sign_up.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\fragment_sign_up.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\fragment_profileview.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\fragment_profileview.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\fragment_accountview.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\fragment_accountview.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\fragment_companysetup.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\fragment_companysetup.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\item_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\item_material.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\fragment_subscription.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\fragment_subscription.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\bottom_update_password.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\bottom_update_password.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\item_employee.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\item_employee.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\bottom_add_employee.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\bottom_add_employee.xml"}, {"merged": "Manaknight.app-mergeDebugResources-105:/layout-sw600dp-v13/fragment_home.xml", "source": "Manaknight.app-main-108:/layout-sw600dp/fragment_home.xml"}, {"merged": "Manaknight.app-mergeDebugResources-105:/layout-sw600dp-v13/fragment_dashboardview.xml", "source": "Manaknight.app-main-108:/layout-sw600dp/fragment_dashboardview.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\item_line.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\item_line.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\activity_main.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\fragment_completesetup.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\fragment_completesetup.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\fragment_add_line_items.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\fragment_add_line_items.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\bottom_select_customer.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\bottom_select_customer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\fragment_login.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\fragment_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\fragment_create_estimation.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\fragment_create_estimation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\fragment_reset_password.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\fragment_reset_password.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\item_line_lineal.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\item_line_lineal.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\fragment_linear_line_item.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\fragment_linear_line_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\bottom_edit_profile.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\bottom_edit_profile.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\item_draw.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\item_draw.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\dialog_resetpassword.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\dialog_resetpassword.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\item_lineal.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\item_lineal.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\dialog_add_square.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\dialog_add_square.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\fragment_linealsetup.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\fragment_linealsetup.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\fragment_material_line_item.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\fragment_material_line_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\fragment_dashboardview.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\fragment_dashboardview.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\fragment_line_items.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\fragment_line_items.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\dialog_add_lineal.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\dialog_add_lineal.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\fragment_squresetup.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\fragment_squresetup.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\fragment_forget_password.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\fragment_forget_password.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\bottom_add_lineal.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\bottom_add_lineal.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\fragment_home.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\fragment_home.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\dialog_forgetpassword.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\dialog_forgetpassword.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\bottom_add_draw.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\bottom_add_draw.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\fragment_create_customer.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\fragment_create_customer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\fragment_materialsetup.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\fragment_materialsetup.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\item_line_total.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\item_line_total.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\item_customer.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\item_customer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\bottom_add_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\bottom_add_material.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\dialog_add_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\dialog_add_material.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\fragment_draws.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\fragment_draws.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\header.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\header.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout-sw600dp-v13\\item_line_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout-sw600dp\\item_line_material.xml"}]