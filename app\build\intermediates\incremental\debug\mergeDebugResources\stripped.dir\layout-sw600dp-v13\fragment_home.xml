<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <include
        android:id="@+id/headerInclude"
        layout="@layout/header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

    <RelativeLayout
        android:id="@+id/container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintWidth_max="500dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/headerInclude"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/innerConstraintLayout"
            android:layout_width="500dp"
            android:layout_height="match_parent"
            android:layout_centerInParent="true">

            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="30dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <!-- Profit & Health Cards -->
                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_centerHorizontal="true"
                            android:padding="16dp"
                            android:layout_marginTop="16dp">

                            <!-- Total Profit Card -->
                            <androidx.cardview.widget.CardView
                                android:layout_width="170dp"
                                android:layout_height="120dp"
                                android:layout_weight="1"
                                app:cardCornerRadius="24dp"
                                app:cardElevation="4dp">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:padding="10dp"
                                    android:background="@color/profit_black"
                                    android:orientation="vertical">

                                    <ImageView
                                        android:layout_width="20dp"
                                        android:layout_height="13dp"
                                        android:src="@drawable/profit_overhead"/>

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="Total profit overhead"
                                        android:textSize="14sp"
                                        android:layout_marginTop="8dp"
                                        android:fontFamily="@font/inter"
                                        android:textFontWeight="400"
                                        android:textColor="#ffffff"
                                        android:alpha="0.8"/>

                                    <HorizontalScrollView
                                        android:id="@+id/scrollTotalProfitOverhead"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:scrollbars="none">
                                        <TextView
                                            android:id="@+id/txtTotalProfitOverhead"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="$0"
                                            android:textSize="24sp"
                                            android:layout_marginTop="8dp"
                                            android:fontFamily="@font/inter"
                                            android:textFontWeight="600"
                                            android:textColor="#ffffff"
                                            android:singleLine="true"
                                            android:ellipsize="end"
                                            app:autoSizeTextType="uniform"
                                            app:autoSizeMinTextSize="16sp"
                                            app:autoSizeMaxTextSize="24sp"
                                            app:autoSizeStepGranularity="1sp" />
                                    </HorizontalScrollView>

                                </LinearLayout>

                            </androidx.cardview.widget.CardView>

                            <View android:layout_width="16dp" android:layout_height="match_parent"/>

                            <!-- Company Health Card -->
                            <androidx.cardview.widget.CardView
                                android:layout_width="170dp"
                                android:layout_height="120dp"
                                android:layout_weight="1"
                                app:cardCornerRadius="24dp"
                                app:cardElevation="4dp">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:padding="10dp"
                                    android:background="@color/profit_brown"
                                    android:orientation="vertical">

                                    <ImageView
                                        android:layout_width="20dp"
                                        android:layout_height="13dp"
                                        android:src="@drawable/compney_health"/>

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="Company Health"
                                        android:textSize="14sp"
                                        android:layout_marginTop="8dp"
                                        android:fontFamily="@font/inter"
                                        android:textFontWeight="400"
                                        android:textColor="#ffffff"
                                        android:alpha="0.8"/>

                                    <HorizontalScrollView
                                        android:id="@+id/scrollCompanyHealth"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:scrollbars="none">
                                        <TextView
                                            android:id="@+id/txtCompanyHealth"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="0%"
                                            android:textSize="24sp"
                                            android:layout_marginTop="8dp"
                                            android:fontFamily="@font/inter"
                                            android:textFontWeight="600"
                                            android:textColor="#EF4444"
                                            android:singleLine="true"
                                            app:autoSizeTextType="uniform"
                                            app:autoSizeMinTextSize="16sp"
                                            app:autoSizeMaxTextSize="24sp"
                                            app:autoSizeStepGranularity="1sp" />
                                    </HorizontalScrollView>

                                </LinearLayout>

                            </androidx.cardview.widget.CardView>

                        </LinearLayout>
                    </RelativeLayout>


                    <!-- View Details Button -->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnViewDetails"
                        android:layout_width="wrap_content"
                        android:layout_height="48dp"
                        android:paddingVertical="0dp"
                        android:textColor="#375DFB"
                        android:textSize="16sp"
                        android:fontFamily="@font/inter"
                        android:textFontWeight="500"
                        app:cornerRadius="8dp"
                        app:strokeWidth="2dp"
                        app:strokeColor="#375DFB"
                        app:backgroundTint="@color/white"
                        android:layout_gravity="center"
                        android:text="View Details" />

                    <!-- Statistics Grid -->
                    <GridLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:columnCount="1"
                        android:rowCount="3"
                        android:layout_marginTop="16dp"
                        android:layout_marginLeft="16dp"
                        android:layout_marginRight="16dp"
                        android:background="@drawable/bg_stat_card"
                        android:padding="16dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center"
                            >

                            <!-- Total Contracts -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                android:gravity="center">

                                <TextView
                                    android:id="@+id/txtTotalContracts"
                                    android:layout_width="100dp"
                                    android:layout_height="wrap_content"
                                    android:text="0"
                                    android:textSize="16sp"
                                    android:layout_marginTop="8dp"
                                    android:fontFamily="@font/inter"
                                    android:textFontWeight="700"
                                    android:textColor="@color/profit_black"
                                    android:singleLine="true"
                                    android:textAlignment="center"
                                    app:autoSizeTextType="uniform"
                                    app:autoSizeMinTextSize="10sp"
                                    app:autoSizeMaxTextSize="16sp"
                                    app:autoSizeStepGranularity="1sp" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Total Contracts"
                                    android:textSize="12sp"
                                    android:layout_marginTop="2dp"
                                    android:fontFamily="@font/inter"
                                    android:textFontWeight="400"
                                    android:textColor="@color/profit_grey" />

                            </LinearLayout>

                            <!-- Total AR -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                android:gravity="center">

                                <TextView
                                    android:id="@+id/txtTotalAR"
                                    android:layout_width="100dp"
                                    android:layout_height="wrap_content"
                                    android:text="0"
                                    android:textSize="16sp"
                                    android:layout_marginTop="8dp"
                                    android:fontFamily="@font/inter"
                                    android:textFontWeight="700"
                                    android:textColor="@color/profit_black"
                                    android:singleLine="true"
                                    android:textAlignment="center"
                                    app:autoSizeTextType="uniform"
                                    app:autoSizeMinTextSize="10sp"
                                    app:autoSizeMaxTextSize="16sp"
                                    app:autoSizeStepGranularity="1sp" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Total AR"
                                    android:textSize="12sp"
                                    android:layout_marginTop="2dp"
                                    android:fontFamily="@font/inter"
                                    android:textFontWeight="400"
                                    android:textColor="@color/profit_grey" />

                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center"
                            android:layout_marginTop="16dp"
                            >

                            <!-- Total Contracts -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                android:gravity="center">

                                <TextView
                                    android:id="@+id/txtMaterialBalance"
                                    android:layout_width="100dp"
                                    android:layout_height="wrap_content"
                                    android:text="$0"
                                    android:textSize="16sp"
                                    android:layout_marginTop="8dp"
                                    android:fontFamily="@font/inter"
                                    android:textFontWeight="700"
                                    android:textColor="@color/profit_black"
                                    android:singleLine="true"
                                    android:textAlignment="center"
                                    app:autoSizeTextType="uniform"
                                    app:autoSizeMinTextSize="10sp"
                                    app:autoSizeMaxTextSize="16sp"
                                    app:autoSizeStepGranularity="1sp" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Material Balance"
                                    android:textSize="12sp"
                                    android:layout_marginTop="2dp"
                                    android:fontFamily="@font/inter"
                                    android:textFontWeight="400"
                                    android:textColor="@color/profit_grey" />

                            </LinearLayout>

                            <!-- Total AR -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                android:gravity="center">

                                <TextView
                                    android:id="@+id/txtMaterialSpent"
                                    android:layout_width="100dp"
                                    android:layout_height="wrap_content"
                                    android:text="$0"
                                    android:textSize="16sp"
                                    android:layout_marginTop="8dp"
                                    android:fontFamily="@font/inter"
                                    android:textFontWeight="700"
                                    android:textColor="@color/profit_black"
                                    android:singleLine="true"
                                    android:textAlignment="center"
                                    app:autoSizeTextType="uniform"
                                    app:autoSizeMinTextSize="10sp"
                                    app:autoSizeMaxTextSize="16sp"
                                    app:autoSizeStepGranularity="1sp" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Material Spent"
                                    android:textSize="12sp"
                                    android:layout_marginTop="2dp"
                                    android:fontFamily="@font/inter"
                                    android:textFontWeight="400"
                                    android:textColor="@color/profit_grey" />

                            </LinearLayout>

                        </LinearLayout>
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center"
                            android:layout_marginTop="16dp"
                            >

                            <!-- Total Contracts -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                android:gravity="center">

                                <TextView
                                    android:id="@+id/txtLaborBalance"
                                    android:layout_width="100dp"
                                    android:layout_height="wrap_content"
                                    android:text="0"
                                    android:textSize="16sp"
                                    android:layout_marginTop="8dp"
                                    android:fontFamily="@font/inter"
                                    android:textFontWeight="700"
                                    android:textColor="@color/profit_black"
                                    android:singleLine="true"
                                    android:textAlignment="center"
                                    app:autoSizeTextType="uniform"
                                    app:autoSizeMinTextSize="10sp"
                                    app:autoSizeMaxTextSize="16sp"
                                    app:autoSizeStepGranularity="1sp" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Labor Balance"
                                    android:textSize="12sp"
                                    android:layout_marginTop="2dp"
                                    android:fontFamily="@font/inter"
                                    android:textFontWeight="400"
                                    android:textColor="@color/profit_grey" />

                            </LinearLayout>

                            <!-- Total AR -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                android:gravity="center">

                                <TextView
                                    android:id="@+id/txtLaborSpent"
                                    android:layout_width="100dp"
                                    android:layout_height="wrap_content"
                                    android:text="0"
                                    android:textSize="16sp"
                                    android:layout_marginTop="8dp"
                                    android:fontFamily="@font/inter"
                                    android:textFontWeight="700"
                                    android:textColor="@color/profit_black"
                                    android:singleLine="true"
                                    android:textAlignment="center"
                                    app:autoSizeTextType="uniform"
                                    app:autoSizeMinTextSize="10sp"
                                    app:autoSizeMaxTextSize="16sp"
                                    app:autoSizeStepGranularity="1sp" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Labor Spent"
                                    android:textSize="12sp"
                                    android:layout_marginTop="2dp"
                                    android:fontFamily="@font/inter"
                                    android:textFontWeight="400"
                                    android:textColor="@color/profit_grey" />

                            </LinearLayout>

                        </LinearLayout>


                    </GridLayout>

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:padding="16dp">

                        <!-- First Row -->
                        <LinearLayout
                            android:id="@+id/firstRow"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center">

                            <LinearLayout
                                android:id="@+id/draftsCard"
                                android:layout_width="match_parent"
                                android:layout_height="100dp"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                android:background="@drawable/bg_stat_card"
                                android:gravity="center">

                                <TextView
                                    android:id="@+id/txtDraftsCount"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="0"
                                    android:textSize="16sp"
                                    android:layout_marginTop="8dp"
                                    android:fontFamily="@font/inter"
                                    android:textFontWeight="700"
                                    android:textColor="#868C98"
                                    android:singleLine="true"
                                    app:autoSizeTextType="uniform"
                                    app:autoSizeMinTextSize="10sp"
                                    app:autoSizeMaxTextSize="16sp"
                                    app:autoSizeStepGranularity="1sp" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Drafts"
                                    android:textSize="12sp"
                                    android:layout_marginTop="2dp"
                                    android:fontFamily="@font/inter"
                                    android:textFontWeight="400"
                                    android:textColor="@color/profit_grey" />

                            </LinearLayout>

                            <View android:layout_width="16dp" android:layout_height="match_parent"/>

                            <LinearLayout
                                android:id="@+id/outstandingCard"
                                android:layout_width="match_parent"
                                android:layout_height="100dp"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                android:background="@drawable/bg_stat_card"
                                android:gravity="center">

                                <TextView
                                    android:id="@+id/txtOutstandingCount"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="0"
                                    android:textSize="16sp"
                                    android:layout_marginTop="8dp"
                                    android:fontFamily="@font/inter"
                                    android:textFontWeight="700"
                                    android:textColor="@color/profit_black"
                                    android:singleLine="true"
                                    app:autoSizeTextType="uniform"
                                    app:autoSizeMinTextSize="10sp"
                                    app:autoSizeMaxTextSize="16sp"
                                    app:autoSizeStepGranularity="1sp" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Outstanding est."
                                    android:textSize="12sp"
                                    android:layout_marginTop="2dp"
                                    android:fontFamily="@font/inter"
                                    android:textFontWeight="400"
                                    android:textColor="@color/profit_grey" />

                            </LinearLayout>
                        </LinearLayout>



                        <!-- Second Row -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center"
                            android:layout_marginTop="-21dp"
                            android:layout_below="@id/addNewEstimation">

                            <LinearLayout
                                android:id="@+id/activeCard"
                                android:layout_width="match_parent"
                                android:layout_height="100dp"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                android:background="@drawable/bg_stat_card"
                                android:gravity="center">

                                <TextView
                                    android:id="@+id/txtActiveCount"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="0"
                                    android:textSize="16sp"
                                    android:layout_marginTop="8dp"
                                    android:fontFamily="@font/inter"
                                    android:textFontWeight="700"
                                    android:textColor="@color/profit_black"
                                    android:singleLine="true"
                                    app:autoSizeTextType="uniform"
                                    app:autoSizeMinTextSize="10sp"
                                    app:autoSizeMaxTextSize="16sp"
                                    app:autoSizeStepGranularity="1sp" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Active"
                                    android:textSize="12sp"
                                    android:layout_marginTop="2dp"
                                    android:fontFamily="@font/inter"
                                    android:textFontWeight="400"
                                    android:textColor="@color/profit_grey" />

                            </LinearLayout>

                            <View android:layout_width="16dp" android:layout_height="match_parent"/>

                            <LinearLayout
                                android:id="@+id/completedCard"
                                android:layout_width="match_parent"
                                android:layout_height="100dp"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                android:background="@drawable/bg_stat_card"
                                android:gravity="center">

                                <TextView
                                    android:id="@+id/txtCompletedCount"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="0"
                                    android:textSize="16sp"
                                    android:layout_marginTop="8dp"
                                    android:fontFamily="@font/inter"
                                    android:textFontWeight="700"
                                    android:textColor="@color/profit_black"
                                    android:singleLine="true"
                                    app:autoSizeTextType="uniform"
                                    app:autoSizeMinTextSize="10sp"
                                    app:autoSizeMaxTextSize="16sp"
                                    app:autoSizeStepGranularity="1sp" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Completed"
                                    android:textSize="12sp"
                                    android:layout_marginTop="2dp"
                                    android:fontFamily="@font/inter"
                                    android:textFontWeight="400"
                                    android:textColor="@color/profit_grey" />

                            </LinearLayout>
                        </LinearLayout>

                        <!-- Floating Action Button -->
                        <ImageView
                            android:id="@+id/addNewEstimation"
                            android:layout_width="58dp"
                            android:layout_height="58dp"
                            android:layout_centerHorizontal="true"
                            android:layout_below="@id/firstRow"
                            android:layout_marginTop="-21dp"
                            app:fabSize="normal"
                            app:srcCompat="@drawable/plus_button"/>
                    </RelativeLayout>

                </LinearLayout>

            </ScrollView>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </RelativeLayout>











    <!--    &lt;!&ndash; Floating Plus Button &ndash;&gt;-->
    <!--    <ImageView-->
    <!--        android:layout_width="50dp"-->
    <!--        android:layout_height="50dp"-->
    <!--        android:src="@drawable/ic_add"-->
    <!--        android:background="@drawable/circle_background"-->
    <!--        android:layout_gravity="center"-->
    <!--        android:layout_marginTop="10dp"/>-->



</androidx.constraintlayout.widget.ConstraintLayout>
