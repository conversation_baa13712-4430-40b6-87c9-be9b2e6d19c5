<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_home" modulePackage="Manaknight" filePath="app\src\main\res\layout\fragment_home.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_home_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="674" endOffset="51"/></Target><Target tag="binding_1" view="LinearLayout"><Expressions/><location startLine="6" startOffset="4" endLine="672" endOffset="18"/></Target><Target id="@+id/headerInclude" tag="binding_1" include="header"><Expressions/><location startLine="13" startOffset="8" endLine="15" endOffset="37"/></Target><Target id="@+id/txtTotalProfitOverhead" view="TextView"><Expressions/><location startLine="76" startOffset="36" endLine="90" endOffset="75"/></Target><Target id="@+id/txtCompanyHealth" view="TextView"><Expressions/><location startLine="136" startOffset="36" endLine="150" endOffset="75"/></Target><Target id="@+id/btnViewDetails" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="163" startOffset="16" endLine="177" endOffset="49"/></Target><Target id="@+id/txtTotalContracts" view="TextView"><Expressions/><location startLine="206" startOffset="28" endLine="220" endOffset="67"/></Target><Target id="@+id/txtTotalAR" view="TextView"><Expressions/><location startLine="242" startOffset="28" endLine="257" endOffset="67"/></Target><Target id="@+id/materialBalanceScroll" view="HorizontalScrollView"><Expressions/><location startLine="290" startOffset="28" endLine="321" endOffset="46"/></Target><Target id="@+id/txtMaterialBalance" view="TextView"><Expressions/><location startLine="304" startOffset="32" endLine="319" endOffset="71"/></Target><Target id="@+id/txtMaterialSpent" view="TextView"><Expressions/><location startLine="343" startOffset="28" endLine="358" endOffset="67"/></Target><Target id="@+id/txtLaborBalance" view="TextView"><Expressions/><location startLine="389" startOffset="28" endLine="404" endOffset="67"/></Target><Target id="@+id/txtLaborSpent" view="TextView"><Expressions/><location startLine="426" startOffset="28" endLine="441" endOffset="67"/></Target><Target id="@+id/firstRow" view="LinearLayout"><Expressions/><location startLine="467" startOffset="20" endLine="549" endOffset="34"/></Target><Target id="@+id/draftsCard" view="LinearLayout"><Expressions/><location startLine="474" startOffset="24" endLine="509" endOffset="38"/></Target><Target id="@+id/txtDraftsCount" view="TextView"><Expressions/><location startLine="483" startOffset="28" endLine="497" endOffset="67"/></Target><Target id="@+id/outstandingCard" view="LinearLayout"><Expressions/><location startLine="513" startOffset="24" endLine="548" endOffset="38"/></Target><Target id="@+id/txtOutstandingCount" view="TextView"><Expressions/><location startLine="522" startOffset="28" endLine="536" endOffset="67"/></Target><Target id="@+id/activeCard" view="LinearLayout"><Expressions/><location startLine="562" startOffset="24" endLine="597" endOffset="38"/></Target><Target id="@+id/txtActiveCount" view="TextView"><Expressions/><location startLine="571" startOffset="28" endLine="585" endOffset="67"/></Target><Target id="@+id/completedCard" view="LinearLayout"><Expressions/><location startLine="601" startOffset="24" endLine="636" endOffset="38"/></Target><Target id="@+id/txtCompletedCount" view="TextView"><Expressions/><location startLine="610" startOffset="28" endLine="624" endOffset="67"/></Target><Target id="@+id/addNewEstimation" view="ImageView"><Expressions/><location startLine="640" startOffset="20" endLine="648" endOffset="62"/></Target></Targets></Layout>